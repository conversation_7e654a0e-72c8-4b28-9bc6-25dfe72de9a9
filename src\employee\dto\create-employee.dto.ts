import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsOptional, IsString } from 'class-validator';

export class CreateEmployeeDto {
  // Basic Info
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  gradeLevelName: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  unitName: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  staffCode: string;

  @ApiProperty()
  @IsString()
  firstName: string;

  @ApiProperty()
  @IsString()
  lastName: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  middleName?: string;

  @ApiProperty()
  @IsString()
  birthday: string;

  // Personal Info
  @ApiProperty()
  @IsString()
  gender: string;

  @ApiProperty()
  @IsString()
  maritalStatus: string;

  @ApiProperty()
  @IsString()
  nationality: string;

  @ApiProperty()
  @IsString()
  stateOfOrigin: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  localGovt?: string;

  @ApiProperty()
  @IsString()
  residentialAddress: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  residentialLocalGovt?: string;

  @ApiProperty()
  @IsString()
  residentialState: string;

  @ApiProperty()
  @IsString()
  residentialCountry: string;

  // Contact
  @ApiProperty()
  @IsString()
  phone1: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  phone2?: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  placeOfBirth?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  religion?: string;

  // Next of Kin
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nextOfKinFullName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nextOfKinRelationship?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nextOfKinPhoneNumber?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEmail()
  nextOfKinEmail?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nextOfKinAddress?: string;

  // Education
  @ApiProperty()
  @IsString()
  @IsOptional()
  highestQualification?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  course: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  institutionName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  dateOfGraduation?: string;

  @ApiProperty()
  @IsString()
  dateEmployed: string;

  // Identity
  @ApiProperty()
  @IsString()
  bvn: string;

  @ApiProperty()
  @IsString()
  nin: string;

  @ApiProperty({ default: false })
  @IsOptional()
  @IsBoolean()
  kycVerified?: boolean;

  // Spouse & Children
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nameOfSpouse?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  noOfChildren?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  accountNumber?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  taxId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  pensionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  pfa?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  dateAppointedToLevel?: string;

  // Guarantor
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  passport?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  certificate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  guarantorPassport?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  guarantorFullname?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  guarantorPhoneNumber?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  guarantorRelationShip?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  guarantorAddress?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  guarantorOccupation?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  guarantorMeansOfIdentification?: string;

  // Work
  @ApiProperty({ required: true })
  @IsString()
  branchName: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  subBranchName?: string;

  @ApiProperty({ required: false })
  @IsString()
  jobTitleName: string;

  @ApiProperty({ required: true })
  @IsString()
  salaryPackageName: string;

  @ApiProperty({ required: true })
  @IsString()
  jobGradeName: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  departmentName?: string;

  @ApiProperty()
  @IsString()
  contractTypeName: string;
}
