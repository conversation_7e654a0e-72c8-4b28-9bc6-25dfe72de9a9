import { Body, Controller, Get, Headers, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { DepartmentService } from './department.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { DeleteDepartmentDto } from './dto/delete-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';

@ApiTags('Department')
@Controller('department')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new department with the provided details.',
    dto: CreateDepartmentDto,
    statusCodes: [],
  })
  @RequirePrivilege('DEPARTMENT|CREATE')
  @Post()
  createDepartment(
    @Body() createDepartmentDto: CreateDepartmentDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.departmentService.createDepartment({
      payload: createDepartmentDto,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all departments.',
    statusCodes: [],
  })
  @RequirePrivilege('DEPARTMENT|VIEW')
  @Get()
  getDepartments(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.departmentService.getDepartments(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update department.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.DEPARTMENT}|${ACTIONS_CONSTANT.update}`)
  @Post('/update')
  updateDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateDepartmentDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.departmentService.updateDepartmentRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete department.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.DEPARTMENT}|${ACTIONS_CONSTANT.delete}`)
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteDepartmentDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.departmentService.deleteDepartmentRequest({
      payload,
      token: token,
    });
  }
}
