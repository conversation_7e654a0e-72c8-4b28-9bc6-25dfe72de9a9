import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { BranchService } from 'src/branch/branch.service';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateSubBranchDto } from './dto/create-sub-branch.dto';
import { DeleteSubBranchDto } from './dto/delete-sub-branch.dto';
import { UpdateSubBranchDto } from './dto/update-sub-branch.dto';

@Injectable()
export class SubBranchService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
    private readonly branchService: BranchService,
  ) {}

  async findSubBranchByNameAndBranch(name: string, branchId: string) {
    const branch = await this.databaseService.subBranch.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive',
        },
        branchId,
        status: 'ACTIVE',
      },
    });

    return branch;
  }

  async findSubBranchByNameAndBranchExcludingId(
    name: string,
    branchId: string,
    companyId: string,
    excludeId: string,
  ) {
    const subBranch = await this.databaseService.subBranch.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        branchId,
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return subBranch;
  }

  async findSubBranchById(id: string, companyId: string) {
    const subBranch = await this.databaseService.subBranch.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return subBranch;
  }

  // Method to find a role by ID or Name
  async getSubBranches(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const subBranches = await this.databaseService.subBranch.findMany({
        where: {
          companyId,
        },
        include: {
          branch: {
            select: {
              name: true,
            },
          },
        },
      });

      return subBranches.map((sb) => ({
        ...sb,
        name: sb.name.split('|')[1],
        branch: sb.branch?.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptSubBranchAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description, branch } = JSON.parse(
          queue.data,
        ) as CreateSubBranchDto;

        // Get branch ID (validation already done at request level)
        const branchExist = await this.branchService.findBranchByName(
          branch,
          companyId,
        );

        await this.databaseService.subBranch.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || '',
            branchId: branchExist!.id,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateSubBranchDto;

        await this.updateSubBranch({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteSubBranchDto;

        await this.deleteSubBranch(payload);

        return true;
      }

      default:
        return false;
    }
  }

  async createSubBranch({
    payload,
    token,
  }: {
    payload: CreateSubBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { branch, name } = payload;

    // Check if branch exists
    const branchExist = await this.branchService.findBranchByName(
      branch,
      decodedToken.companyId,
    );

    if (!branchExist) {
      throw new NotFoundException("Branch doesn't exist");
    }

    // Check if sub-branch already exists with this name in this branch
    const subBranchExist = await this.findSubBranchByNameAndBranch(
      `${decodedToken.companyId}|${name}`,
      branchExist.id,
    );

    if (subBranchExist) {
      throw new ConflictException(
        `Sub branch with ${name} already exists in this branch`,
      );
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      companyId: decodedToken.companyId,
      action: ACTIONS_CONSTANT.create,
      module: MODULE_CONSTANT.SUB_BRANCH,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateSubBranchRequest({
    payload,
    token,
  }: {
    payload: UpdateSubBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id } = payload;

    const existingSubBranch = await this.findSubBranchById(
      id,
      decodedToken.companyId,
    );

    if (!existingSubBranch) {
      throw new NotFoundException(`Sub branch with id ${id} not found.`);
    }

    // If branch is being updated, validate it exists
    if (payload.branch) {
      const branchExist = await this.branchService.findBranchByName(
        payload.branch,
        decodedToken.companyId,
      );

      if (!branchExist) {
        throw new NotFoundException("Branch doesn't exist");
      }

      // If name is also being updated or we have a new branch, check for conflicts
      if (payload.name) {
        const existingSubBranchName =
          await this.findSubBranchByNameAndBranchExcludingId(
            payload.name,
            branchExist.id,
            decodedToken.companyId,
            id,
          );

        if (existingSubBranchName) {
          throw new ConflictException(
            `Sub branch with name ${payload.name} already exists in this branch`,
          );
        }
      }
    } else if (payload.name) {
      // If only name is being updated, check within current branch
      const existingSubBranchName =
        await this.findSubBranchByNameAndBranchExcludingId(
          payload.name,
          existingSubBranch.branchId,
          decodedToken.companyId,
          id,
        );

      if (existingSubBranchName) {
        throw new ConflictException(
          `Sub branch with name ${payload.name} already exists in this branch`,
        );
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.SUB_BRANCH,
      requestedBy: decodedToken.name,
    });
  }

  async updateSubBranch({
    payload,
    companyId,
  }: {
    payload: UpdateSubBranchDto;
    companyId: string;
  }) {
    const { description, name, id, branch } = payload;

    const updateData: Prisma.SubBranchUpdateInput = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description !== undefined) {
      updateData.description = description;
    }

    if (branch) {
      // Get branch ID from branch name (validation already done at request level)
      const branchExist = await this.branchService.findBranchByName(
        branch,
        companyId,
      );
      updateData.branch = { connect: { id: branchExist!.id } };
    }

    await this.databaseService.subBranch.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteSubBranchRequest({
    payload,
    token,
  }: {
    payload: DeleteSubBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id } = payload;

    const existingSubBranch = await this.findSubBranchById(
      id,
      decodedToken.companyId,
    );

    if (!existingSubBranch) {
      throw new NotFoundException(`Sub branch with id ${id} not found.`);
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.SUB_BRANCH,
      requestedBy: decodedToken.name,
    });
  }

  async deleteSubBranch(payload: DeleteSubBranchDto) {
    const { id } = payload;

    await this.databaseService.subBranch.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }
}
