import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  ParseIntPipe,
  Post,
  Req,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { RoleService } from './role.service';

@ApiTags('Role')
@Controller('role')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new role with the provided details.',
    dto: CreateRoleDto,
    statusCodes: [],
  })
  @RequirePrivilege('ROLE|CREATE')
  @Post()
  createRole(@Body() createRoleDto: CreateRoleDto, @Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.roleService.createRoleRequest({
      payload: createRoleDto,
      token: token!,
    });
  }
  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all roles.',
    statusCodes: [],
  })
  @RequirePrivilege('ROLE|VIEW')
  @Get()
  getRoles(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.roleService.getRoles(token!);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Find role by id',
    statusCodes: [],
  })
  @RequirePrivilege('ROLE|VIEW')
  @Get('/read/role-by-id/:roleId')
  getRoleById(
    @Headers('authorization') authHeader: string,
    @Param('roleId', ParseIntPipe) roleId: number,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.roleService.findRole({ identifier: roleId, token });
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update a role',
    statusCodes: [],
  })
  @RequirePrivilege('ROLE|UPDATE')
  @Post('/update')
  updateRole(@Body() payload: UpdateRoleDto, @Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.roleService.updateRoleRequest({ token: token!, payload });
  }
}
