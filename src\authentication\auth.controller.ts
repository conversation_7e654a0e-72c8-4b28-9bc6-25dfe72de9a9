import {
  Body,
  Controller,
  Headers,
  HttpCode,
  HttpStatus,
  Post,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';
import { ResendOtpDto } from 'src/otp/dto/resend-otp.dto';
import { OtpService } from 'src/otp/otp.service';
import { AuthService } from './auth.service';
import { AuthChangePasswordDto } from './dto/auth-change-password.dto';
import { AuthCompleteForgotPasswordDto } from './dto/auth-complete-forgot-password.dto';
import { AuthInitiateForgotPasswordDto } from './dto/auth-initiate-forgot-password.dto';
import { AuthSwitchDto } from './dto/auth-switch.dto';
import { AuthUserDto } from './dto/auth-user.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly otpService: OtpService,
  ) {}

  @ApiBody({ type: AuthUserDto })
  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('login')
  auth(@Body() payload: AuthUserDto) {
    return this.authService.signIn(payload);
  }

  @ApiBody({ type: AuthInitiateForgotPasswordDto })
  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('initiate/forgot-password')
  initiateForgotPassword(@Body() payload: AuthInitiateForgotPasswordDto) {
    return this.authService.initiateForgotPassword(payload);
  }

  @ApiBody({ type: AuthCompleteForgotPasswordDto })
  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('complete/forgot-password')
  completeForgotPassword(@Body() payload: AuthCompleteForgotPasswordDto) {
    return this.authService.completeForgotPassword(payload);
  }

  @ApiBody({ type: AuthSwitchDto })
  @Post('switch-comapny')
  switchCompany(
    @Body() payload: AuthSwitchDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.authService.switchCompany({ payload, token });
  }

  @ApiBody({ type: ResendOtpDto })
  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('resend-otp')
  resendOtp(@Body() payload: ResendOtpDto) {
    return this.otpService.resendOtp(payload);
  }

  @HttpCode(HttpStatus.OK)
  @Post('change-password')
  async changePassword(
    @Body() payload: AuthChangePasswordDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.authService.changePassword({ payload, token });
  }
}
