import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma, Role } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { DeleteRoleDto } from './dto/delete-role.dto';

@Injectable()
export class RoleService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  // Method to find a role by ID or Name
  async findRole({
    identifier,
    token,
  }: {
    identifier: number | string;
    token: string;
  }) {
    let role:
      | (Role & {
          RolePrivilege: {
            privilege: {
              id: string;
              name: string;
              status: string;
            };
          }[];
        })
      | null;

    const decodedToken = await this.authTokenService.decodeToken(token);

    const { companyId } = decodedToken;

    // If identifier is a number, search by id, otherwise by name
    if (typeof identifier === 'number') {
      role = await this.databaseService.role.findUnique({
        where: { id: identifier, companyId },
        include: {
          RolePrivilege: {
            select: {
              privilege: {
                select: {
                  id: true,
                  name: true,
                  status: true,
                },
              },
            },
          },
        },
      });
    } else {
      role = await this.databaseService.role.findUnique({
        where: { name: identifier, companyId },
        include: {
          RolePrivilege: {
            select: {
              privilege: {
                select: {
                  id: true,
                  name: true,
                  status: true,
                },
              },
            },
          },
        },
      });
    }

    if (!role) {
      throw new NotFoundException(`Role with id ${identifier} not found.`);
    }

    const { RolePrivilege, ...roleData } = role;

    return {
      ...roleData,
      privileges: RolePrivilege.map((rp) => rp.privilege),
    };
  }

  async findRoleById(id: number, companyId: string) {
    const role = await this.databaseService.role.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return role;
  }

  async findRoleByName(name: string, companyId: string) {
    const role = await this.databaseService.role.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive',
        },
        companyId,
        status: 'ACTIVE',
      },
    });

    return role;
  }

  async findRoleByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: number,
  ) {
    const role = await this.databaseService.role.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return role;
  }

  // Method to find a role by ID or Name
  async getRoles(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { companyId } = decodedToken;
    const roles = await this.databaseService.role.findMany({
      where: {
        OR: [
          {
            isPublic: true,
          },
          {
            companyId,
          },
        ],
      },
    });

    return roles;
  }

  // Method to create a new role
  async acceptRoleAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    // Check if the role already exists by name
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { description, name, privileges } = JSON.parse(
          queue.data,
        ) as CreateRoleDto;

        await this.databaseService.$transaction(async (tx) => {
          const newRole = await tx.role.create({
            data: {
              name,
              description,
              createdBy: requestedBy,
              approvedBy,
              company: {
                connect: {
                  id: companyId,
                },
              },
            },
          });

          await tx.rolePrivilege.createMany({
            data: privileges.map((privilegeName) => ({
              roleId: newRole.id,
              privilegeName,
            })),
          });
        });
        return true;
      }

      case 'UPDATE': {
        const payload = JSON.parse(queue.data) as UpdateRoleDto;

        await this.updateRole({
          payload,
          requestedBy,
          approvedBy,
        });

        return true;
      }

      case 'DELETE': {
        const payload = JSON.parse(queue.data) as DeleteRoleDto;

        await this.deleteRole(payload);

        return true;
      }

      default:
        return false;
    }
  }

  async updateRole({
    payload,
    requestedBy,
    approvedBy,
  }: {
    payload: UpdateRoleDto;
    requestedBy: string;
    approvedBy: string;
  }) {
    const { description, name, privileges, id } = payload;

    await this.databaseService.$transaction(async (tx) => {
      const updateData: Prisma.RoleUpdateInput = {
        createdBy: requestedBy,
        approvedBy,
      };

      if (name) {
        updateData.name = name;
      }

      if (description !== undefined) {
        updateData.description = description;
      }

      const updatedRole = await tx.role.update({
        where: {
          id,
        },
        data: updateData,
      });

      if (privileges) {
        await tx.rolePrivilege.deleteMany({
          where: { roleId: updatedRole.id },
        });

        const uniquePrivileges = [...new Set(privileges)];

        await tx.rolePrivilege.createMany({
          data: uniquePrivileges.map((privilegeName) => ({
            roleId: updatedRole.id,
            privilegeName,
          })),
        });
      }
    });
  }

  async deleteRole(payload: DeleteRoleDto) {
    await this.databaseService.role.update({
      where: {
        id: payload.id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }

  private async validatePrivilegesExistOrThrow(privilegeNames: string[]) {
    const uniquePrivilegeNames = [...new Set(privilegeNames)].filter(Boolean);

    if (uniquePrivilegeNames.length === 0) return;

    const existing = await this.databaseService.privilege.findMany({
      where: {
        name: { in: uniquePrivilegeNames },
        status: 'ACTIVE',
        isPrivate: false,
      },
      select: { name: true },
    });

    const existingNames = new Set(existing.map((p) => p.name));
    const missing = uniquePrivilegeNames.filter((n) => !existingNames.has(n));

    if (missing.length > 0) {
      throw new NotFoundException(
        `Privilege does not exist: ${missing.join(', ')}`,
      );
    }
  }

  async createRoleRequest({
    payload,
    token,
  }: {
    payload: CreateRoleDto;
    token: string;
  }) {
    const { name, privileges, description } = payload;

    const decodedToken = await this.authTokenService.decodeToken(token);

    // Validate role name doesn't already exist
    const roleExist = await this.findRoleByName(name, decodedToken.companyId);

    if (roleExist) {
      throw new ConflictException(`Role with name ${name} already exists`);
    }

    // Validate provided privileges exist and are assignable
    await this.validatePrivilegesExistOrThrow(privileges || []);

    return await this.authorizationRequestMaker.queueRequest({
      payload: {
        name,
        privileges,
        description,
      },
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.ROLE,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateRoleRequest({
    payload,
    token,
  }: {
    payload: UpdateRoleDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id, name, privileges } = payload;

    if (id === 100) {
      throw new ForbiddenException('No privilege to modify SUPER_ADMIN Role');
    }

    // Validate role exists
    const existingRole = await this.findRoleById(id, decodedToken.companyId);

    if (!existingRole) {
      throw new NotFoundException(`Role with id ${id} not found.`);
    }

    // Validate role name doesn't already exist on another record
    if (name) {
      const existingRoleName = await this.findRoleByNameExcludingId(
        name,
        decodedToken.companyId,
        id,
      );

      if (existingRoleName) {
        throw new ConflictException(`Role with name ${name} already exists`);
      }
    }

    // Validate provided privileges exist and are assignable (if provided)
    if (Array.isArray(privileges)) {
      await this.validatePrivilegesExistOrThrow(privileges);
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.ROLE,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async deleteRoleRequest({
    payload,
    token,
  }: {
    payload: DeleteRoleDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id } = payload;

    if (id === 100) {
      throw new BadRequestException('No privilege to modify SUPER_ADMIN Role');
    }

    // Validate role exists
    const existingRole = await this.findRoleById(id, decodedToken.companyId);

    if (!existingRole) {
      throw new NotFoundException(`Role with id ${id} not found.`);
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.ROLE,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }
}
