import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { Constants } from 'src/common/utils/constants';
import { DatabaseService } from 'src/database/database.service';
import { MailService } from 'src/mail/mail.service';
import { CreateUserDto } from './dto/create-user.dto';
import { DeleteUserDto } from './dto/delete-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { isNullOrUndefined } from 'util';
import { NotFoundException } from '@nestjs/common';

@Injectable()
export class UsersService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cryptoService: CryptoService,
    private readonly authTokenService: AuthTokenService,
    private readonly mailService: MailService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}
  async createUser({
    payload,
    companyId,
    approvedBy,
    createdBy,
  }: {
    payload: CreateUserDto;
    companyId: string;
    approvedBy: string;
    createdBy: string;
  }) {
    const {
      email,
      password,
      twoFactorEnabled,
      isRoot,
      roleId,
      branches,
      hasAccessToAllBranches,
      name,
    } = payload;

    const companyExist = await this.databaseService.company.findUnique({
      where: {
        id: companyId,
      },
      include: {
        Account: true,
      },
    });

    if (!companyExist) {
      throw new BadRequestException('Company not found.');
    }

    const passwordHash = await this.cryptoService.hash(password);

    const newUser = await this.databaseService.user.create({
      data: {
        email,
        password: passwordHash,
        twoFactorEnabled,
        hasAccessToAllBranches,
        isRoot,
        companyId,
        roleId,
        name,
        createdBy,
        status: 'NEW',
        approvedBy,
      },
    });

    if (branches && branches.length > 0) {
      const userBranches = branches.map((branchId) => ({
        branchId: branchId,
        userId: newUser.id,
      }));
      await this.databaseService.userBranch.createMany({
        data: userBranches,
      });
    }

    const loginUrl = `https://${companyExist.Account.slug}.${Constants.CLIENT_URL}/auth/login?cid=${companyId}`;

    await this.mailService.sendEmail({
      subject: `${companyExist.name} payroll management system`,
      html: `<h1>Login Credentials</h1><p>Company Account ID: ${companyExist.accountId}</p><p>Your Email: ${email}</p><p>Your Password: ${password}</p><p>Login URL: <a href="${loginUrl}">${loginUrl}</a></p>`,
      context: {
        name: 'John Doe',
      },
      email: newUser.email,
    });

    return {
      user: newUser,
      message:
        'User created successfully. An onboarding email has been sent to the user.',
    };
  }

  async findUserById(id: string) {
    if (!id) {
      throw new BadRequestException('User Id is required.');
    }
    return await this.databaseService.user.findUnique({
      where: {
        id,
      },
    });
  }

  async updateUser(payload: UpdateUserDto) {
    const {
      email,
      twoFactorEnabled,
      isRoot,
      roleId,
      branches,
      hasAccessToAllBranches,
      name,
      id,
    } = payload;

    const updatedUser = await this.databaseService.$transaction(
      async (prisma) => {
        // Update the User record
        const user = await prisma.user.update({
          where: { id },
          data: {
            email,
            twoFactorEnabled,
            hasAccessToAllBranches,
            isRoot,
            roleId,
            name,
          },
        });

        if (branches !== undefined && branches !== null) {
          // Delete all existing UserBranch entries for this user
          await prisma.userBranch.deleteMany({
            where: { userId: user.id },
          });

          if (branches.length > 0) {
            const userBranchesToCreate = branches.map((branchId) => ({
              branchId: branchId,
              userId: user.id,
            }));
            await prisma.userBranch.createMany({
              data: userBranchesToCreate,
            });
          }
        }

        return user;
      },
    );

    return updatedUser;
  }

  async deleteUser({
    payload,
    companyId,
  }: {
    payload: DeleteUserDto;
    companyId: string;
  }) {
    const { id } = payload;

    const userExist = await this.databaseService.user.findUnique({
      where: {
        id,
        companyId,
      },
    });

    if (!userExist) {
      throw new BadRequestException('User not found.');
    }

    const deletedUser = await this.databaseService.user.update({
      where: {
        id: userExist.id,
      },
      data: {
        status: 'INACTIVE',
      },
    });

    return deletedUser;
  }

  async getUsers(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { companyId } = decodedToken;
    const users = await this.databaseService.user.findMany({
      where: {
        companyId,
        status: {
          not: 'INACTIVE',
        },
      },
      include: {
        role: {
          select: {
            name: true,
          },
        },
        branches: {
          select: {
            branch: {
              select: {
                name: true,
                id: true,
              },
            },
          },
        },
      },
    });

    return users.map((user) => ({
      ...user,
      password: undefined,
      branches: user.branches.map(({ branch }) => ({
        ...branch,
        name: branch.name.split('|')[1],
      })),
    }));
  }

  async createUserRequest({
    payload,
    token,
  }: {
    payload: CreateUserDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { email, roleId, branches, hasAccessToAllBranches } = payload;

    // Validate email uniqueness (case-insensitive) within the company
    const userExist = await this.databaseService.user.findFirst({
      where: {
        email: {
          equals: email,
          mode: 'insensitive',
        },
        companyId: decodedToken.companyId,
      },
    });

    if (userExist) {
      throw new BadRequestException(
        'A user with this email already exists in the company.',
      );
    }

    // Validate that the role exists and belongs to the company or is public
    const roleExist = await this.databaseService.role.findFirst({
      where: {
        id: roleId,
        OR: [{ companyId: decodedToken.companyId }, { isPublic: true }],
        status: 'ACTIVE',
      },
    });

    if (!roleExist) {
      throw new NotFoundException('Role not found.');
    }

    // Validate branch requirements based on hasAccessToAllBranches
    if (!hasAccessToAllBranches && (!branches || branches.length === 0)) {
      throw new BadRequestException(
        'Branches are required when hasAccessToAllBranches is false.',
      );
    }

    // Validate branch IDs if provided
    if (branches && branches.length > 0) {
      const existingBranches = await this.databaseService.branch.findMany({
        where: {
          id: { in: branches },
          companyId: decodedToken.companyId,
        },
        select: { id: true },
      });

      const existingBranchIds = existingBranches.map((b) => b.id);

      const invalidBranchIds = branches.filter(
        (id) => !existingBranchIds.includes(id),
      );

      if (invalidBranchIds.length > 0) {
        throw new NotFoundException(
          `Branch not found: ${invalidBranchIds.join(', ')}`,
        );
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.USER,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async deleteUserRequest({
    payload,
    token,
  }: {
    payload: DeleteUserDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Validate that user exists and belongs to the company
    const userExist = await this.databaseService.user.findUnique({
      where: {
        id: payload.id,
        companyId: decodedToken.companyId,
      },
    });

    if (!userExist) {
      throw new BadRequestException('User not found.');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload: { ...payload, name: userExist.name, email: userExist.email },
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.USER,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateUserRequest({
    payload,
    token,
  }: {
    payload: UpdateUserDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id, email, roleId, branches, hasAccessToAllBranches } = payload;

    // Validate that user exists
    const userExist = await this.databaseService.user.findUnique({
      where: {
        id,
        companyId: decodedToken.companyId,
      },
    });

    if (!userExist) {
      throw new BadRequestException('User not found.');
    }

    // Validate email uniqueness if email is being updated
    if (email && email !== userExist.email) {
      const emailExists = await this.databaseService.user.findFirst({
        where: {
          email: {
            equals: email,
            mode: 'insensitive',
          },
          companyId: decodedToken.companyId,
          id: {
            not: id,
          },
        },
      });

      if (emailExists) {
        throw new BadRequestException(
          'A user with this email already exists in the company.',
        );
      }
    }

    // Validate role if roleId is provided
    if (roleId) {
      const roleExist = await this.databaseService.role.findFirst({
        where: {
          id: roleId,
          OR: [{ companyId: decodedToken.companyId }, { isPublic: true }],
          status: 'ACTIVE',
        },
      });

      if (!roleExist) {
        throw new NotFoundException(
          'Invalid role ID. Role not found or not accessible for this company.',
        );
      }
    }

    // Validate branch requirements if hasAccessToAllBranches is being set to false
    if (
      hasAccessToAllBranches === false &&
      (!branches || branches.length === 0)
    ) {
      throw new BadRequestException(
        'Branches are required since user does not have access to all branches.',
      );
    }

    // Validate branch IDs if provided
    if (branches && branches.length > 0) {
      const existingBranches = await this.databaseService.branch.findMany({
        where: {
          id: { in: branches },
          companyId: decodedToken.companyId,
        },
        select: { id: true },
      });

      const existingBranchIds = existingBranches.map((b) => b.id);

      const invalidBranchIds = branches.filter(
        (id) => !existingBranchIds.includes(id),
      );

      if (invalidBranchIds.length > 0) {
        throw new BadRequestException(
          `Invalid branch IDs: ${invalidBranchIds.join(', ')}. These branches either do not exist or do not belong to this company.`,
        );
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.USER,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async acceptUserAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const payload = JSON.parse(queue.data) as CreateUserDto;

        await this.createUser({
          approvedBy,
          companyId,
          createdBy: requestedBy,
          payload,
        });

        return true;
      }
      case 'UPDATE': {
        const payload = JSON.parse(queue.data) as UpdateUserDto;

        await this.updateUser(payload);

        return true;
      }
      case 'DELETE': {
        const payload = JSON.parse(queue.data) as DeleteUserDto;

        await this.deleteUser({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }
}
