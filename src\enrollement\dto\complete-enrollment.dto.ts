import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CompleteEnrollmentDto {
  @ApiProperty({
    example: 'ENR-20240421-K93LZX',
    description: 'The unique ref sent on initiate enrollment.',
  })
  @IsString()
  @IsNotEmpty()
  uniqueRef: string;

  @ApiProperty({
    example: '233445',
    description: 'OTP sent to the company email.',
  })
  @IsString()
  @IsNotEmpty()
  otp: string;
}
