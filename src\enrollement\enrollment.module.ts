import { Module } from '@nestjs/common';
import { AccountService } from 'src/account/account.service';
import { CryptoModule } from 'src/common/crypto/crypto.module';
import { DatabaseModule } from 'src/database/database.module';
import { MailModule } from 'src/mail/mail.module';
import { MailService } from 'src/mail/mail.service';
import { OtpService } from 'src/otp/otp.service';
import { EnrollmentController } from './enrollment.controller';
import { EnrollmentService } from './enrollment.service';

@Module({
  imports: [DatabaseModule, CryptoModule, MailModule],
  providers: [EnrollmentService, OtpService, MailService, AccountService],
  controllers: [EnrollmentController],
})
export class EnrollmentModule {}
