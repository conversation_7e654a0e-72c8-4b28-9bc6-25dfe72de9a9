import { ConflictException, Injectable } from '@nestjs/common';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { DatabaseService } from 'src/database/database.service';
import * as changeCase from 'change-case';

@Injectable()
export class AuthorizationRequestMaker {
  constructor(private readonly databaseService: DatabaseService) {}

  async queueRequest({
    payload,
    companyId,
    action,
    module,
    requestedBy,
  }: {
    payload: Record<string, any>;
    companyId: string;
    module: MODULE_CONSTANT;
    action: ACTIONS_CONSTANT;
    requestedBy: string;
  }) {
    if (
      [
        ACTIONS_CONSTANT.create,
        ACTIONS_CONSTANT.update,
        ACTIONS_CONSTANT.delete,
      ].includes(action)
    ) {
      await this.validateNoPendingRequest(payload, companyId, module, action);
    }

    await this.databaseService.authorizationQueue.create({
      data: {
        data: JSON.stringify(payload),
        action,
        module,
        requestedBy,
        company: {
          connect: {
            id: companyId,
          },
        },
      },
    });
  }

  private async validateNoPendingRequest(
    payload: Record<string, any>,
    companyId: string,
    module: MODULE_CONSTANT,
    action: ACTIONS_CONSTANT,
  ) {
    // For USER create and update requests, use email as the identifier; otherwise fall back to name
    const identifierField = module === MODULE_CONSTANT.USER ? 'email' : 'name';

    const identifierValue = payload[identifierField];

    if (!identifierValue) {
      // If there's no relevant identifier, we can't validate - let it proceed
      return;
    }

    // Check for existing pending requests for this identifier, module, and company
    const existingPendingRequest =
      await this.databaseService.authorizationQueue.findFirst({
        where: {
          companyId,
          module,
          status: 'PENDING',
          data: {
            contains: `"${identifierField}":"${identifierValue}"`,
            mode: 'insensitive',
          },
        },
      });

    if (existingPendingRequest) {
      const actionText =
        action === ACTIONS_CONSTANT.create
          ? 'creation'
          : action === ACTIONS_CONSTANT.update
            ? 'update'
            : 'deletion';

      throw new ConflictException(
        `A ${actionText} request for "${identifierValue}" in ${changeCase.noCase(module.toLowerCase())} is already pending authorization. Please wait for approval or rejection before submitting a new request.`,
      );
    }
  }
}
