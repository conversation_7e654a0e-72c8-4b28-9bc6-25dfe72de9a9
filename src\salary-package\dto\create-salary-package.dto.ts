import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';

class SalaryItemDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNumber()
  amount: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;
}

export class CreateSalaryPackageDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiProperty()
  @IsNumber()
  baseSalary: number;

  @ApiProperty()
  @IsNumber()
  pensionRate: number;

  @ApiProperty()
  @IsNumber()
  nhfRate: number;

  @ApiProperty()
  @IsNumber()
  taxAmount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  currency: string;

  // Additional Allowance Fields
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  apprenticeAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  housingAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  transportAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  utilityAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  selfMaintenanceAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  hazardOrEntertainmentAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  furnitureAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  fuelSubsidy?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  domesticStaffAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  childEducationSubsidy?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  levelProficiencyAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  responsibilityAllowance?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  monthlyGrossSalary?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  annualGrossSalary?: number;

  // Existing structured fields
  @ApiProperty({ type: [SalaryItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SalaryItemDto)
  allowances: SalaryItemDto[];

  @ApiProperty({ type: [SalaryItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SalaryItemDto)
  deductions: SalaryItemDto[];
}
