import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  Post,
  Req,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreatePayrollUploadDto } from './dto/create-payroll-update.dto';
import { DisbursePayrollDto } from './dto/disburse-payroll.dto';
import { PayrollService } from './payroll.service';

@ApiTags('Payroll')
@Controller('payroll')
export class PayrollController {
  constructor(private readonly payrollService: PayrollService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new department with the provided details.',
    dto: CreatePayrollUploadDto,
    statusCodes: [],
  })
  @RequirePrivilege('PAYROLL|CREATE')
  @Post('/create')
  createSalaryPackage(
    @Body() payload: CreatePayrollUploadDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.payrollService.createPayroll({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all payroll uploads.',
    statusCodes: [],
  })
  @RequirePrivilege('PAYROLL|VIEW')
  @Get('/read')
  getPayrollUploads(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.payrollService.getPayrollUploads(token!);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all payroll upload records',
    statusCodes: [],
  })
  @RequirePrivilege('PAYROLL|VIEW')
  @Get('/read/payroll-record-by-payroll-upload-id/:payrollUploadId')
  getPayrollRecordsByPayrollUploadId(
    @Param('payrollUploadId') payrollUploadId: string,
  ) {
    return this.payrollService.getPayrollRecordByPayrollUploadId(
      payrollUploadId,
    );
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all payroll upload batch',
    statusCodes: [],
  })
  @RequirePrivilege('PAYROLL|VIEW')
  @Get('/read/payroll-batch-by-payroll-upload-id/:payrollUploadId')
  getPayrollBatchByPayrollUploadId(
    @Param('payrollUploadId') payrollUploadId: string,
  ) {
    return this.payrollService.getPayrollBatchByPayrollUploadId(
      payrollUploadId,
    );
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Dispatch payroll',
    statusCodes: [],
  })
  @RequirePrivilege('PAYROLL|UPDATE')
  @Post('/update/disburse')
  disbursePayroll(
    @Body() payload: DisbursePayrollDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader?.split(' ')[1];
    return this.payrollService.disbursePayrollRequest({ payload, token });
  }
}
