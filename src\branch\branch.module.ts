import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { BranchController } from './branch.controller';
import { BranchService } from './branch.service';
import { RegionModule } from 'src/region/region.module';

@Module({
  imports: [DatabaseModule, RegionModule],
  providers: [BranchService, AuthTokenService, TaxJurisdictionService],
  controllers: [BranchController],
})
export class BranchModule {}
