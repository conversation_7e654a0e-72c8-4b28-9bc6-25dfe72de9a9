import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsEmail,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({
    description: 'The full name of the user.',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'The email address of the user.',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'The password for the user account.',
    example: 'strongpassword123',
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    description: 'The list of branch IDs the user belongs to.',
    example: ['1', '2'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  branches?: string[];

  @ApiProperty({
    description: 'The role ID assigned to the user.',
    example: 100,
  })
  @IsInt()
  roleId: number;

  @ApiProperty({
    description: 'Whether two-factor authentication is enabled for the user.',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @ApiProperty({
    description: 'Whether the user can access all branches.',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  hasAccessToAllBranches?: boolean;

  @ApiProperty({
    description:
      'Whether the user is a root user (created along with the company).',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isRoot?: boolean;
}
