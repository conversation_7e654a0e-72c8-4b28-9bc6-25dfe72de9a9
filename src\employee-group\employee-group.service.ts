import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateEmployeeGroupDto } from './dto/create-employee-group.dto';
import { DeleteEmployeeGroupDto } from './dto/delete-employee-group.dto';
import { UpdateEmployeeGroupDto } from './dto/update-employee-group.dto';

@Injectable()
export class EmployeeGroupService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findEmployeeGroup({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const employeeGroup = await this.databaseService.gradeLevel.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return employeeGroup;
  }

  async findEmployeeGroupById(id: string, companyId: string) {
    const employeeGroup = await this.databaseService.gradeLevel.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return employeeGroup;
  }

  async findEmployeeGroupByName(name: string, companyId: string) {
    const employeeGroup = await this.databaseService.gradeLevel.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return employeeGroup;
  }

  async findEmployeeGroupByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const employeeGroup = await this.databaseService.gradeLevel.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return employeeGroup;
  }

  // Method to find a role by ID or Name
  async getEmployeeGroups(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const employeeGroups = await this.databaseService.gradeLevel.findMany({
        where: {
          companyId,
          status: {
            not: 'INACTIVE',
          },
        },
      });

      return employeeGroups.map((employeeGroup) => ({
        ...employeeGroup,
        name: employeeGroup.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptEmployeeGroupAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateEmployeeGroupDto;

        await this.databaseService.gradeLevel.create({
          data: {
            name: `${companyId}|${name}`,
            description,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }
      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateEmployeeGroupDto;

        await this.updateGradeLevel({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteEmployeeGroupDto;

        await this.deleteGradeLevel(payload);

        return true;
      }
      default:
        return false;
    }
  }

  async createEmployeeGroup({
    payload,
    token,
  }: {
    payload: CreateEmployeeGroupDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const employeeGroupExist = await this.findEmployeeGroupByName(
      payload.name,
      decodedToken.companyId,
    );

    if (employeeGroupExist) {
      throw new ConflictException(
        `Employee group with ${payload.name} already exists`,
      );
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      companyId: decodedToken.companyId,
      action: ACTIONS_CONSTANT.create,
      module: MODULE_CONSTANT.EMPLOYEE_GROUP,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateGradeLevelRequest({
    payload,
    token,
  }: {
    payload: UpdateEmployeeGroupDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id } = payload;

    const existingEmployeeGroup = await this.findEmployeeGroupById(
      id,
      decodedToken.companyId,
    );

    if (!existingEmployeeGroup) {
      throw new NotFoundException(`Employee group with id ${id} not found.`);
    }

    if (payload.name) {
      const existingEmployeeGroupName =
        await this.findEmployeeGroupByNameExcludingId(
          payload.name,
          decodedToken.companyId,
          id,
        );

      if (existingEmployeeGroupName) {
        throw new ConflictException(
          `Employee group with name ${payload.name} already exists`,
        );
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.EMPLOYEE_GROUP,
      requestedBy: decodedToken.name,
    });
  }

  async updateGradeLevel({
    payload,
    companyId,
  }: {
    payload: UpdateEmployeeGroupDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    const updateData: Prisma.GradeLevelUpdateInput = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description) {
      updateData.description = description;
    }

    await this.databaseService.gradeLevel.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteGradeLevelRequest({
    payload,
    token,
  }: {
    payload: DeleteEmployeeGroupDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id } = payload;

    const existingEmployeeGroup = await this.findEmployeeGroupById(
      id,
      decodedToken.companyId,
    );

    if (!existingEmployeeGroup) {
      throw new NotFoundException(`Employee group with id ${id} not found.`);
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.EMPLOYEE_GROUP,
      requestedBy: decodedToken.name,
    });
  }

  async deleteGradeLevel(payload: DeleteEmployeeGroupDto) {
    await this.databaseService.gradeLevel.update({
      where: {
        id: payload.id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }
}
