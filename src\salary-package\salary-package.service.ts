import { ConflictException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateSalaryPackageDto } from './dto/create-salary-package.dto';

@Injectable()
export class SalaryPackageService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findSalaryPackage({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const department = this.databaseService.salaryPackage.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return department;
  }

  async findSalaryPackageByName(name: string, companyId: string) {
    const salaryPackage = await this.databaseService.salaryPackage.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return salaryPackage;
  }

  async findSalaryPackageByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const salaryPackage = await this.databaseService.salaryPackage.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return salaryPackage;
  }

  // Method to find a role by ID or Name
  async getSalaryPackages(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const salaryPackages = await this.databaseService.salaryPackage.findMany({
        where: {
          companyId,
        },
      });

      return salaryPackages.map((salaryPackage) => ({
        ...salaryPackage,
        name: salaryPackage.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptSalaryPackageAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const {
          name,
          description,
          baseSalary,
          currency,
          nhfRate,
          pensionRate,
          taxAmount,
          annualGrossSalary,
          apprenticeAllowance,
          childEducationSubsidy,
          domesticStaffAllowance,
          fuelSubsidy,
          furnitureAllowance,
          hazardOrEntertainmentAllowance,
          housingAllowance,
          levelProficiencyAllowance,
          monthlyGrossSalary,
          responsibilityAllowance,
          selfMaintenanceAllowance,
          transportAllowance,
          utilityAllowance,
        } = JSON.parse(queue.data) as CreateSalaryPackageDto;

        await this.databaseService.salaryPackage.create({
          data: {
            name: `${companyId}|${name}`,
            description,
            companyId,
            baseSalary,
            currency,
            nhfRate,
            pensionRate,
            taxAmount,
            createdBy: requestedBy,
            approvedBy,
            annualGrossSalary,
            apprenticeAllowance,
            childEducationSubsidy,
            domesticStaffAllowance,
            fuelSubsidy,
            furnitureAllowance,
            hazardOrEntertainmentAllowance,
            housingAllowance,
            levelProficiencyAllowance,
            monthlyGrossSalary,
            responsibilityAllowance,
            selfMaintenanceAllowance,
            transportAllowance,
            utilityAllowance,
          },
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createSalaryPackage({
    payload,
    token,
  }: {
    payload: CreateSalaryPackageDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the salary package already exists by name
    const salaryPackageExist = await this.findSalaryPackageByName(
      payload.name,
      decodedToken.companyId,
    );

    if (salaryPackageExist) {
      throw new ConflictException('Salary package already exists');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.SALARY_PACKAGE,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }
}
