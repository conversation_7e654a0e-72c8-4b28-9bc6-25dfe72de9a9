import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUrl,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';

export class AccountDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address for the root user account.',
    required: true,
  })
  @IsEmail()
  @MaxLength(150)
  email: string;

  @ApiProperty({
    example: '<PERSON> Do<PERSON>',
    description: 'Name of the root user.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  name: string;

  @ApiProperty({
    example: 'P@ssw0rd!',
    description: 'Password for the root user.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  password: string;
}

export class InitiateEnrollmentDto {
  // ============================
  // 📧 Company Email
  // ============================
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the company (also used for root user).',
    required: true,
  })
  @IsEmail()
  @MaxLength(150)
  email: string;

  // ============================
  // ☎️ Phone Number
  // ============================
  @ApiProperty({
    example: '+*************',
    description: 'The phone number of the company.',
    required: true,
  })
  @IsPhoneNumber()
  @MaxLength(20)
  phoneNumber: string; // ✅ should be string, not number

  // ============================
  // 🏢 Company Info
  // ============================
  @ApiProperty({
    example: 'Example Corp',
    description: 'Company name.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(150)
  name: string;

  @ApiProperty({
    example: 'https://example.com/logo.png',
    description: 'URL of the company logo.',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  @MaxLength(200)
  logo?: string;

  @ApiProperty({
    example: '123 Example St',
    description: 'Company address.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(200)
  address: string;

  @ApiProperty({
    example: 'Lagos',
    description: 'State of the company.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  state: string;

  @ApiProperty({
    example: 'Ikeja',
    description: 'City of the company.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  city: string;

  @ApiProperty({
    example: '100001',
    description: 'ZIP/postal code.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(20)
  zipCode?: string;

  @ApiProperty({
    example: 'https://company.com',
    description: 'Company website URL.',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  @MinLength(3)
  @MaxLength(200)
  website?: string;

  @ApiProperty({
    example: 'We build amazing software.',
    description: 'Brief description of the company.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    example: 'REG123456',
    description: 'Company registration number.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z0-9-]+$/, {
    message:
      'Registration number must contain only letters, numbers, and hyphens',
  })
  registrationNumber?: string;

  @ApiProperty({
    example: 'Technology',
    description: 'Industry the company belongs to.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  industry?: string;

  @ApiProperty({
    example: 'Software Development',
    description: 'Type of the industry.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  industryType?: string;

  @ApiProperty({
    example: 'Nigeria',
    description: 'Country where the company is based.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  country: string;

  // ============================
  // 👤 Root User Info
  // ============================
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address for the root user account.',
    required: true,
  })
  @IsEmail()
  @MaxLength(150)
  accountEmail: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the root user.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(150)
  accountName: string;

  @ApiProperty({
    example: 'P@ssw0rd!',
    description: 'Password for the root user.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  password: string;
}
