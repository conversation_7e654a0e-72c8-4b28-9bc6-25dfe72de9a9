import { ApiProperty } from '@nestjs/swagger';
import {
  A<PERSON>yNotEmpty,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class CreateRoleDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiProperty({
    example: ['string'],
    description: 'Array of permission IDs to assign to the role',
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  privileges: string[];
}
