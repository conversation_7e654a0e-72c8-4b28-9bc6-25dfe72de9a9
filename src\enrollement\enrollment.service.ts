import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AccountService } from 'src/account/account.service';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseService } from 'src/database/database.service';
import { MailService } from 'src/mail/mail.service';
import { OtpService } from 'src/otp/otp.service';
import { CompleteEnrollmentDto } from './dto/complete-enrollment.dto';
import { InitiateEnrollmentDto } from './dto/initiate-enrollment.dto';

@Injectable()
export class EnrollmentService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly otpService: OtpService,
    private readonly cryptoService: CryptoService,
    private readonly accountService: AccountService,
    private readonly mailService: MailService,
  ) {}

  private generateUniqueRef(prefix = 'ENR'): string {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const random = Math.random().toString(36).substring(2, 8).toUpperCase(); // 6 chars
    return `${prefix}-${date}-${random}`; // e.g., ENR-********-K93LZX
  }

  async initiateEnrollment(payload: InitiateEnrollmentDto) {
    console.log(payload);

    const { password, accountEmail: email, accountName: name } = payload;

    try {
      // Check if the company already exists by email (case-insensitive)
      const existingCompany = await this.databaseService.account.findFirst({
        where: {
          email: {
            equals: email,
            mode: 'insensitive',
          },
        },
      });

      if (existingCompany) {
        throw new BadRequestException(
          'An account with provided email already exists',
        );
      }

      // Check if there's already a pending enrollment with this email (case-insensitive)
      const existingEnrollment =
        await this.databaseService.enrollment.findFirst({
          where: {
            accountEmail: {
              equals: email,
              mode: 'insensitive',
            },
          },
        });

      if (existingEnrollment) {
        throw new BadRequestException(
          'An enrollment with provided email already exists',
        );
      }

      const uniqueRef = this.generateUniqueRef();

      const passwordHash = await this.cryptoService.hash(password);

      // Create new enrollment
      const newEnrollment = await this.databaseService.enrollment.create({
        data: {
          ...payload,
          uniqueRef,
          password: passwordHash,
          accountEmail: email,
          accountName: name,
        },
      });

      const otp = await this.otpService.requestOtp({
        email,
      });

      return {
        uniqueRef: newEnrollment.uniqueRef,
        otpRef: otp.otpRef,
      };
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async findEnrollment(payload: { uniqueRef: string }) {
    const { uniqueRef } = payload;
    try {
      // Check if the company already exists by email
      const enrollment = await this.databaseService.enrollment.findUnique({
        where: { uniqueRef },
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      return enrollment;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async completeEnrollment(payload: CompleteEnrollmentDto) {
    const { otp, uniqueRef } = payload;

    try {
      // Check if the company already exists by email
      const existingEnrollment = await this.findEnrollment({ uniqueRef });

      if (!existingEnrollment.id) {
        throw new NotFoundException('Enrollment not found');
      }

      // Verify the OTP
      const verifyOtp = await this.otpService.verifyOtp({
        code: otp,
        email: existingEnrollment.accountEmail,
      });

      if (!verifyOtp.otpVerified) {
        throw new BadRequestException('Invalid or expired OTP');
      }
      // Create the company

      const baseSlug = this.accountService.generateSlug(
        existingEnrollment.name,
      );

      const slug = await this.accountService.ensureUniqueSlug(baseSlug);
      // const count = await this.databaseService.account.count();

      await this.databaseService.$transaction(async (tx) => {
        const {
          address,
          email,
          name,
          phoneNumber,
          city,
          country,
          createdAt,
          description,
          industry,
          industryType,
          logo,
          registrationNumber,
          state,
          password,
          website,
          zipCode,
          accountEmail,
          accountName,
        } = existingEnrollment;

        const newAccount = await tx.account.create({
          data: {
            // accountId: ********** + count,
            email: accountEmail,
            name: accountName,
            password,
            slug,
            roleId: 100,
          },
        });

        await tx.company.create({
          data: {
            address,
            accountId: newAccount.id,
            email,
            name,
            phoneNumber,
            city,
            country,
            createdAt,
            description,
            industry,
            industryType,
            logo,
            registrationNumber,
            state,
            website,
            zipCode,
            emailVerified: true,
          },
        });

        await tx.enrollment.delete({
          where: { uniqueRef },
        });
      });

      // Send enrollment completion email with company URL slug
      await this.mailService.sendEmail({
        subject: 'Welcome to CoreHR - Your Company Account is Ready!',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #2563eb; margin: 0;">CoreHR</h1>
            </div>
            
            <div style="background-color: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1e293b; margin-top: 0;">🎉 Congratulations! Your Company Account is Ready</h2>
              <p style="color: #475569; font-size: 16px; line-height: 1.5;">
                Hello <strong>${existingEnrollment.accountName}</strong>,
              </p>
              <p style="color: #475569; font-size: 16px; line-height: 1.5;">
                Your enrollment has been completed successfully! Your company account for <strong>${existingEnrollment.name}</strong> is now active and ready to use.
              </p>
              
              <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
                <h3 style="color: #1e293b; margin-top: 0;">Your Company URL</h3>
                <p style="color: #475569; margin: 10px 0;">
                  Access your company instance using this URL:
                </p>
                <div style="background-color: #f1f5f9; padding: 15px; border-radius: 6px; font-family: 'Courier New', monospace; word-break: break-all;">
                  <strong style="color: #2563eb; font-size: 18px;">https://${slug}.payroll.corestepmfb.com</strong>
                </div>
                <p style="color: #64748b; font-size: 14px; margin: 10px 0 0 0;">
                  💡 Bookmark this URL for easy access to your dashboard
                </p>
              </div>

              <div style="background-color: #ecfdf5; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #059669; margin: 0 0 10px 0;">Next Steps:</h4>
                <ul style="color: #047857; margin: 0; padding-left: 20px;">
                  <li>Log in using your email: <strong>${existingEnrollment.accountEmail}</strong></li>
                  <li>Complete your company profile setup</li>
                  <li>Add your employees and start managing payroll</li>
                </ul>
              </div>
            </div>
            
            <div style="text-align: center; padding: 20px; border-top: 1px solid #e2e8f0;">
              <p style="color: #64748b; font-size: 14px; margin: 0;">
                Need help? Contact our support team or visit our help center.
              </p>
              <p style="color: #64748b; font-size: 12px; margin: 10px 0 0 0;">
                This email was sent to ${existingEnrollment.accountEmail}
              </p>
            </div>
          </div>
        `,
        context: {
          companyName: existingEnrollment.name,
          accountName: existingEnrollment.accountName,
          slug: slug,
          companyUrl: `https://${slug}.payroll.corestepmfb.com`,
        },
        email: existingEnrollment.accountEmail,
      });

      return {
        slug,
      };
    } catch (error) {
      console.log('Enrollment error', error);

      throw error;
    }
  }
}
