import { BadRequestException, Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { MailService } from 'src/mail/mail.service';
import { RequestOtpDto } from './dto/request-otp.dto';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { VerifyAuthOtpDto } from './dto/verify-auth-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';

@Injectable()
export class OtpService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly mailService: MailService,
  ) {}

  private generateOtp(length = 6): string {
    return Math.floor(100000 + Math.random() * 900000)
      .toString()
      .slice(0, length);
  }

  async requestOtp(dto: RequestOtpDto) {
    const code = this.generateOtp();

    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    if (dto.companyId) {
      await this.databaseService.otp.deleteMany({
        where: {
          email: dto.email,
          companyId: dto.companyId,
        },
      });
    } else {
      await this.databaseService.otp.deleteMany({
        where: {
          email: dto.email,
          companyId: null,
        },
      });
    }

    const otp = await this.databaseService.otp.create({
      data: {
        email: dto.email,
        companyId: dto.companyId,
        code,
        expiresAt,
      },
    });

    // TODO: send OTP via email or SMS here
    await this.mailService.sendEmail({
      subject: 'CoreHR - Your One-Time Password (OTP)',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">CoreHR</h1>
          </div>
          
          <div style="background-color: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #1e293b; margin-top: 0;">Your One-Time Password</h2>
            <p style="color: #475569; font-size: 16px; line-height: 1.5;">
              We've received a request to verify your identity. Please use the following OTP to complete your verification:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <div style="background-color: #2563eb; color: white; font-size: 32px; font-weight: bold; padding: 20px; border-radius: 8px; letter-spacing: 3px; display: inline-block;">
                ${otp.code}
              </div>
            </div>
            
            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0;">
              <p style="color: #92400e; margin: 0; font-weight: 600;">
                ⏰ This OTP is valid for 5 minutes only
              </p>
              <p style="color: #92400e; margin: 5px 0 0 0; font-size: 14px;">
                Please enter this code promptly to complete your verification.
              </p>
            </div>
          </div>
          
          <div style="border-top: 1px solid #e2e8f0; padding-top: 20px;">
            <p style="color: #64748b; font-size: 14px; margin: 0;">
              If you didn't request this OTP, please ignore this email or contact our support team if you have concerns.
            </p>
            <p style="color: #64748b; font-size: 14px; margin: 10px 0 0 0;">
              Best regards,<br>
              The CoreHR Team
            </p>
          </div>
        </div>
      `,
      context: {
        name: 'User',
      },
      email: otp.email,
    });
    return { message: 'OTP sent', otpRef: otp.id }; // Remove `code` in production!
  }

  async resendOtp(dto: ResendOtpDto) {
    const code = this.generateOtp();

    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    const otpExist = await this.databaseService.otp.findUnique({
      where: {
        id: dto.otpRef,
      },
    });

    if (!otpExist) {
      throw new BadRequestException('Invalid request');
    }
    const otp = await this.databaseService.otp.update({
      where: {
        id: otpExist.id,
      },
      data: {
        code,
        expiresAt,
      },
    });

    if (!otp) {
      throw new BadRequestException('Invalid or expired OTP');
    }

    await this.mailService.sendEmail({
      subject: 'CoreHR - Your One-Time Password (OTP)',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">CoreHR</h1>
          </div>
          
          <div style="background-color: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #1e293b; margin-top: 0;">Your One-Time Password</h2>
            <p style="color: #475569; font-size: 16px; line-height: 1.5;">
              We've received a request to verify your identity. Please use the following OTP to complete your verification:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <div style="background-color: #2563eb; color: white; font-size: 32px; font-weight: bold; padding: 20px; border-radius: 8px; letter-spacing: 3px; display: inline-block;">
                ${code}
              </div>
            </div>
            
            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0;">
              <p style="color: #92400e; margin: 0; font-weight: 600;">
                ⏰ This OTP is valid for 5 minutes only
              </p>
              <p style="color: #92400e; margin: 5px 0 0 0; font-size: 14px;">
                Please enter this code promptly to complete your verification.
              </p>
            </div>
          </div>
          
          <div style="border-top: 1px solid #e2e8f0; padding-top: 20px;">
            <p style="color: #64748b; font-size: 14px; margin: 0;">
              If you didn't request this OTP, please ignore this email or contact our support team if you have concerns.
            </p>
            <p style="color: #64748b; font-size: 14px; margin: 10px 0 0 0;">
              Best regards,<br>
              The CoreHR Team
            </p>
          </div>
        </div>
      `,
      context: {
        name: 'User',
      },
      email: otpExist.email,
    });

    return;
  }

  async verifyOtp(dto: VerifyOtpDto) {
    const otp = await this.databaseService.otp.findFirst({
      where: {
        email: dto.email,
        code: dto.code,
        expiresAt: { gt: new Date() },
      },
    });
    console.log(otp);

    if (!otp) {
      throw new BadRequestException('Invalid or expired OTP');
    }

    await this.databaseService.otp.deleteMany({
      where: { email: dto.email },
    });

    return { otpVerified: true };
  }

  async verifyAuthOtp(dto: VerifyAuthOtpDto) {
    const otp = await this.databaseService.otp.findFirst({
      where: {
        email: dto.email,
        code: dto.code,
        companyId: dto.companyId,
        expiresAt: { gt: new Date() },
      },
    });
    console.log(otp);

    if (!otp) {
      throw new BadRequestException('Invalid or expired OTP');
    }

    await this.databaseService.otp.deleteMany({
      where: { email: dto.email },
    });

    return { otpVerified: true };
  }
}
