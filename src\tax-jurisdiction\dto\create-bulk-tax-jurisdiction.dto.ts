import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';
import { CreateTaxJurisdictionDto } from './create-tax-jurisdiction.dto';

export class CreateBulkTaxJurisdictionDto {
  @ApiProperty({ type: [CreateTaxJurisdictionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTaxJurisdictionDto)
  items: Array<CreateTaxJurisdictionDto>;
}
