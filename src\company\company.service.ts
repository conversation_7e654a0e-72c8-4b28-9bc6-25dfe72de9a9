import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { CreateCompanyDto } from './dto/create-company.dto';

@Injectable()
export class CompanyService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  async findCompany({
    email,
    accountId,
  }: {
    email: string;
    accountId: number;
  }) {
    return this.databaseService.company.findUnique({
      where: {
        accountId_email: {
          email,
          accountId,
        },
      },
    });
  }

  async findCompanyByName(name: string) {
    return this.databaseService.company.findFirst({
      where: {
        name: {
          mode: 'insensitive',
          equals: name,
        },
      },
    });
  }

  async findCompanyByPhone(phoneNumber: string) {
    return this.databaseService.company.findFirst({
      where: {
        phoneNumber,
      },
    });
  }

  async findCompanyByWebsite(website: string) {
    return this.databaseService.company.findFirst({
      where: {
        website,
      },
    });
  }

  async findCompanyByRegistrationNumber(registrationNumber: string) {
    return this.databaseService.company.findFirst({
      where: {
        registrationNumber,
      },
    });
  }

  async createCompany({
    payload,
    token,
  }: {
    payload: CreateCompanyDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { email, name, phoneNumber, website, registrationNumber } = payload;

    const accountExist = await this.databaseService.account.findUnique({
      where: { id: decodedToken.accountId },
    });
    if (!accountExist) {
      throw new NotFoundException('Account not found');
    }

    const companyExist = await this.findCompany({
      email,
      accountId: decodedToken.accountId,
    });
    if (companyExist) {
      throw new ConflictException('Company with email already exists');
    }

    const companyNameExist = await this.findCompanyByName(name);
    if (companyNameExist) {
      throw new ConflictException('Company with this name already exists');
    }

    const companyPhoneExist = await this.findCompanyByPhone(phoneNumber);
    if (companyPhoneExist) {
      throw new ConflictException(
        'Company with this phone number already exists',
      );
    }

    if (website) {
      const companyWebsiteExist = await this.findCompanyByWebsite(website);
      if (companyWebsiteExist) {
        throw new ConflictException('Company with this website already exists');
      }
    }

    if (registrationNumber) {
      const companyRegistrationExist =
        await this.findCompanyByRegistrationNumber(registrationNumber);
      if (companyRegistrationExist) {
        throw new ConflictException(
          'Company with this registration number already exists',
        );
      }
    }

    const newCompany = await this.databaseService.company.create({
      data: {
        ...payload,
        accountId: accountExist.id,
      },
    });

    return newCompany;
  }
}
