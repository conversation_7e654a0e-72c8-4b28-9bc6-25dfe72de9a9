import { Body, Controller, Get, Param, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { BulkCreateEmployeeDto } from './dto/bulk-create-employee.dto';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { FindEmployeeByEmailDto } from './dto/find-employee.dto';
import { EmployeeService } from './employee.service';

@ApiTags('Employee')
@Controller('employee')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all company staff',
    statusCodes: [],
  })
  @RequirePrivilege('EMPLOYEE|VIEW')
  @Get('')
  findCompanyEmployees(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.employeeService.findCompanyEmployees(token!);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all employee jobs',
    statusCodes: [],
  })
  @RequirePrivilege('EMPLOYEE|VIEW')
  @Get('/batch/read')
  getEmployeeBatch(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.employeeService.getEmployeeBatch(token!);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all employee jobs',
    statusCodes: [],
  })
  @RequirePrivilege('EMPLOYEE|VIEW')
  @Get('/batch-record/read/:id')
  getEmployeeBatchRecordById(@Param('id') id: string) {
    return this.employeeService.getEmployeeBatchRecord(id);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get a company staff',
    statusCodes: [],
  })
  @RequirePrivilege('EMPLOYEE|VIEW')
  @Get('/email/:email')
  findEmployeeByEmail(
    @Param('email') email: FindEmployeeByEmailDto['email'],
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.employeeService.findEmployeeByEmail({ email, token });
  }

  // Create a single staff
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new employee with the provided details.',
    dto: CreateEmployeeDto,
    statusCodes: [],
  })
  @RequirePrivilege('EMPLOYEE|CREATE')
  @Post()
  createEmployee(@Req() request: Request, @Body() payload: CreateEmployeeDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.employeeService.createEmployee({ data: payload, token });
  }

  // Bulk create staff
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Bulk creation of new staff with the provided details.',
    dto: BulkCreateEmployeeDto,
    statusCodes: [],
  })
  @RequirePrivilege('EMPLOYEE|CREATE')
  @Post('create/bulk')
  createBulkEmployee(
    @Req() request: Request,
    @Body() payload: BulkCreateEmployeeDto,
  ) {
    const token = request.headers.authorization?.split(' ')[1];
    return this.employeeService.createBulkEmployee({ data: payload, token });
  }
}
