import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { DeleteRegionDto } from './dto/delete-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';

@Injectable()
export class RegionService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findRegionById(id: string, companyId: string) {
    const region = await this.databaseService.region.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return region;
  }

  async findRegionByName(name: string, companyId: string) {
    const region = await this.databaseService.region.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return region;
  }

  async findRegionByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const region = await this.databaseService.region.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return region;
  }

  async findRegion({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const region = await this.databaseService.region.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return region;
  }
  // Method to find a role by ID or Name
  async getRegions(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const regions = await this.databaseService.region.findMany({
        where: {
          companyId,
        },
      });

      return regions.map((region) => ({
        ...region,
        name: region.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptRegionAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(queue.data) as CreateRegionDto;
        await this.databaseService.region.create({
          data: {
            name: `${companyId}|${name}`,
            description,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateRegionDto;

        await this.updateRegion({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteRegionDto;

        await this.deleteRegion(payload);

        return true;
      }

      default:
        return false;
    }
  }

  async createRegionRequest({
    payload,
    token,
  }: {
    payload: CreateRegionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const regionExist = await this.findRegionByName(
      payload.name,
      decodedToken.companyId,
    );

    if (regionExist) {
      throw new ConflictException(`Region with ${payload.name} already exists`);
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      companyId: decodedToken.companyId,
      action: ACTIONS_CONSTANT.create,
      module: MODULE_CONSTANT.REGION,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateRegionRequest({
    payload,
    token,
  }: {
    payload: UpdateRegionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { name, id } = payload;

    const existingRegion = await this.findRegionById(
      id,
      decodedToken.companyId,
    );

    if (!existingRegion) {
      throw new NotFoundException(`Region with id ${id} not found.`);
    }

    if (payload.name) {
      const existingRegionName = await this.findRegionByNameExcludingId(
        payload.name,
        decodedToken.companyId,
        id,
      );

      if (existingRegionName) {
        throw new ConflictException(
          `Region with name ${payload.name} already exists`,
        );
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.REGION,
      requestedBy: decodedToken.name,
    });
  }

  async updateRegion({
    payload,
    companyId,
  }: {
    payload: UpdateRegionDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    const updateData: Prisma.RegionUpdateInput = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description) {
      updateData.description = description;
    }

    await this.databaseService.region.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteRegionRequest({
    payload,
    token,
  }: {
    payload: DeleteRegionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { id } = payload;

    const existingRegion = await this.findRegionById(
      id,
      decodedToken.companyId,
    );

    if (!existingRegion) {
      throw new NotFoundException(`Region with id ${id} not found.`);
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.REGION,
      requestedBy: decodedToken.name,
    });
  }

  async deleteRegion(payload: DeleteRegionDto) {
    await this.databaseService.region.update({
      where: {
        id: payload.id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }
}
