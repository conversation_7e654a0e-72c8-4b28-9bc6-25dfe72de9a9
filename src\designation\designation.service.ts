import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { DeleteDesignationDto } from './dto/delete-designation.dto';
import { CreateDesignationDto } from './dto/create-designation.dto';
import { UpdateDesignationDto } from './dto/update-designation.dto';

@Injectable()
export class DesignationService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findDesignation({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const designation = await this.databaseService.jobTitle.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return designation;
  }

  async findDesignationById(id: string) {
    const designation = await this.databaseService.jobTitle.findUnique({
      where: {
        id,
        status: 'ACTIVE',
      },
    });

    return designation;
  }

  async findDesignationByName(name: string, companyId: string) {
    const designation = await this.databaseService.jobTitle.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return designation;
  }

  async findDesignationByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const designation = await this.databaseService.jobTitle.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return designation;
  }

  // Method to find a role by ID or Name
  async getDesignations(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { companyId } = decodedToken;
    const designations = await this.databaseService.jobTitle.findMany({
      where: {
        companyId,
        status: {
          not: 'INACTIVE',
        },
      },
      include: {
        _count: {
          select: {
            employee: true,
          },
        },
      },
    });

    return designations.map((designation) => ({
      ...designation,
      name: designation.name.split('|')[1],
    }));
  }

  // Method to create a new role
  async acceptDesignationAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as unknown as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateDesignationDto;

        await this.databaseService.jobTitle.create({
          data: {
            name: `${companyId}|${name}`,
            description,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateDesignationDto;

        await this.updateDesignation({
          companyId,
          payload,
        });

        return true;
      }

      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteDesignationDto;

        await this.deleteDesignation(payload);

        return true;
      }
      default:
        return false;
    }
  }

  async createDesignation({
    payload,
    token,
  }: {
    payload: CreateDesignationDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the designation already exists by name
    const designationExist = await this.findDesignationByName(
      payload.name,
      decodedToken.companyId,
    );

    if (designationExist) {
      throw new ConflictException('Designation already exists');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.DESIGNATION,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateDesignation({
    payload,
    companyId,
  }: {
    payload: UpdateDesignationDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    const updateData: Prisma.JobTitleUpdateInput = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description) {
      updateData.description = description;
    }

    await this.databaseService.jobTitle.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteDesignation(payload: DeleteDesignationDto) {
    const { id } = payload;

    await this.databaseService.jobTitle.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }

  async queueRequest({
    payload,
    companyId,
    action,
    module,
    requestedBy,
  }: {
    payload: any;
    companyId: string;
    module: MODULE_CONSTANT;
    action: ACTIONS_CONSTANT;
    requestedBy: string;
  }) {
    await this.databaseService.authorizationQueue.create({
      data: {
        data: JSON.stringify(payload),
        action,
        companyId,
        module,
        requestedBy,
      },
    });
    return { message: 'Request successful and pending authorztion' };
  }

  async deleteDesignationRequest({
    payload,
    token,
  }: {
    payload: DeleteDesignationDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const designation = await this.findDesignationById(payload.id);

    if (!designation) {
      throw new NotFoundException('Designation not found');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload: { ...payload, name: designation.name.split('|')[1] },
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.DESIGNATION,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateDesignationRequest({
    payload,
    token,
  }: {
    payload: UpdateDesignationDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the designation exists
    const existingDesignation = await this.findDesignationById(payload.id);

    if (!existingDesignation) {
      throw new NotFoundException('Designation not found.');
    }

    // Check if another designation exists with the same name (excluding current one)
    if (payload.name) {
      const existingDesignationName =
        await this.findDesignationByNameExcludingId(
          payload.name,
          decodedToken.companyId,
          payload.id,
        );

      if (existingDesignationName) {
        throw new ConflictException('Designation already exists');
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.DESIGNATION,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }
}
