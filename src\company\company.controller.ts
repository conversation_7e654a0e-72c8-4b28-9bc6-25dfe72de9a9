import { Body, Controller, Headers, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle, Throttle } from '@nestjs/throttler';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';

@ApiTags('Company')
@SkipThrottle()
@Controller('company')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new company with the provided details.',
    dto: CreateCompanyDto,
    statusCodes: [],
  })
  @Throttle({ short: { ttl: 6000, limit: 5 } }) // Allow 5 requests per minute
  // Add privilege for create company using module and action constant
  @RequirePrivilege(`${MODULE_CONSTANT.USER}|${ACTIONS_CONSTANT.create}`)
  @Post('create')
  @Public()
  createCompany(
    @Body() payload: CreateCompanyDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.companyService.createCompany({ payload, token });
  }
}
