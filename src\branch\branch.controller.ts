import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { BranchService } from './branch.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { DeleteBranchDto } from './dto/delete-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';

@ApiTags('Branch')
@Controller('branch')
export class BranchController {
  constructor(private readonly branchService: BranchService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new branch with the provided details.',
    dto: CreateBranchDto,
    statusCodes: [],
  })
  @RequirePrivilege('BRANCH|CREATE')
  @Post()
  createBranch(@Body() payload: CreateBranchDto, @Req() request: Request) {
    console.log('Creating branch with payload:');

    const token = request.headers.authorization?.split(' ')[1];

    return this.branchService.createBranch({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all branches.',
    statusCodes: [],
  })
  @RequirePrivilege('BRANCH|VIEW')
  @Get()
  getBranches(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.branchService.getBranches(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update a branch.',
    statusCodes: [],
    dto: UpdateBranchDto,
  })
  @RequirePrivilege('BRANCH|UPDATE')
  @Post('/update')
  updateBranch(@Req() request: Request, @Body() payload: UpdateBranchDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.branchService.updateBranchRequest({ token: token!, payload });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete a branch.',
    statusCodes: [],
    dto: DeleteBranchDto,
  })
  @RequirePrivilege('BRANCH|DELETE')
  @Post('/delete')
  deleteBranch(@Req() request: Request, @Body() payload: DeleteBranchDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.branchService.deleteBranchRequest({ token: token!, payload });
  }
}
