/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Injectable,
  NotImplementedException,
} from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AxiosResponse } from 'axios';
import { format, parse } from 'date-fns';
import { lastValueFrom, Observable } from 'rxjs';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { ValidationService } from 'src/validation/validation.service';
import { BulkCreateEmployeeDto } from './dto/bulk-create-employee.dto';
import { CreateBankAccountDto } from './dto/create-bank-account.dto';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { FindEmployeeByEmailDto } from './dto/find-employee.dto';
import { PowerbankAuthenticationResponse } from './inteface/PowerbankAuthenticationResponse';

@Injectable()
export class EmployeeService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly httpService: HttpService,
    private readonly kycService: ValidationService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  private readonly powerBankAuthenticateUrlDev =
    'https://sandbox.corestepbank.com/logon';
  private readonly powerBankCreateAccountUrlDev =
    'https://sandbox.corestepbank.com/customerCreate';

  private readonly powerBankUploadAccountUrlDev =
    'https://sandbox.corestepbank.com/uploadCustomers';

  private readonly powerbankAuthenticate = async () => {
    try {
      const response$ = this.httpService.post(
        this.powerBankAuthenticateUrlDev,
        {
          username: process.env.POWERBANK_USERNAME,
          userBankCode: process.env.POWERBANK_BANK_CODE,
          userPassword: process.env.POWERBANK_PASSWORD,
          source: 'WEB',
          userCountryCode: '234',
        },
      ) as Observable<AxiosResponse<PowerbankAuthenticationResponse>>;

      const response = await lastValueFrom(response$);

      console.log(response);

      return response.data;
    } catch (error: unknown) {
      console.log('Error authenticating with Qore ID:', error);
      throw new Error('QoreId Authentication failed');
    }
  };

  private readonly formatPhoneNumber = (phone: string) => {
    const digitsOnly = phone.replace(/\D/g, ''); // Remove all non-digits
    return digitsOnly.startsWith('0') ? digitsOnly.slice(1) : digitsOnly;
  };

  private readonly formatDate = ({
    dateStr,
    fallback = '',
  }: {
    dateStr: string;
    fallback?: string;
  }) => {
    if (!dateStr) return fallback;

    try {
      const parsedDate = parse(dateStr, 'dd/MM/yyyy', new Date());
      return format(parsedDate, 'dd-MMM-yyyy'); // e.g., 22-Mar-2023
    } catch {
      return fallback;
    }
  };

  async validateCreateEmployee(
    data: CreateEmployeeDto & { companyId: string },
  ) {
    const {
      email,
      bvn,
      nin,
      staffCode,
      companyId,
      branchName,
      subBranchName,
      jobTitleName,
      salaryPackageName,
      unitName,
      jobGradeName,
      contractTypeName,
      departmentName,
      gradeLevelName,
      phone1,
      birthday,
      dateEmployed,
      dateAppointedToLevel,
      dateOfGraduation,
      taxId,
      pensionId,
      pfa,
    } = data;

    const errors: string[] = [];

    const isValidDate = (value: string) => /^\d{2}\/\d{2}\/\d{4}$/.test(value);

    if (!isValidDate(birthday)) {
      errors.push(`"birthday" must be in the format dd/mm/yyyy.`);
    }

    if (dateEmployed && !isValidDate(dateEmployed)) {
      errors.push(`"Date Employed" must be in the format dd/mm/yyyy.`);
    }

    if (dateAppointedToLevel && !isValidDate(dateAppointedToLevel)) {
      errors.push(
        `"Date Appointed To Level" must be in the format dd/mm/yyyy.`,
      );
    }

    if (dateOfGraduation && !isValidDate(dateOfGraduation)) {
      errors.push(`"Date Of Graduation" must be in the format dd/mm/yyyy.`);
    }

    // 1. Parallel uniqueness checks
    const [staffCodeExists, employeeExist, emailExists, financialIdExists] =
      await Promise.all([
        this.databaseService.employee.findUnique({
          where: { staffCode: `${companyId}|${staffCode}` },
        }),
        this.databaseService.employee.findFirst({
          where: {
            OR: [
              { companyId, phone1 },
              { companyId, bvn },
              { companyId, nin },
            ],
          },
        }),
        email
          ? this.databaseService.employee.findFirst({
              where: { email, companyId },
            })
          : null,
        // Check for financial identifiers uniqueness
        (() => {
          const financialIdConditions: Array<{
            taxId?: string;
            pensionId?: string;
            pfa?: string;
          }> = [];
          if (taxId) financialIdConditions.push({ taxId });
          if (pensionId) financialIdConditions.push({ pensionId });
          if (pfa) financialIdConditions.push({ pfa });

          return financialIdConditions.length > 0
            ? this.databaseService.employee.findFirst({
                where: {
                  companyId,
                  OR: financialIdConditions,
                },
              })
            : null;
        })(),
      ]);

    if (staffCodeExists) {
      errors.push(`Staff code "${staffCode}" already exists for this company.`);
    }

    if (employeeExist) {
      if (employeeExist.phone1 === phone1)
        errors.push(
          `Phone number "${phone1}" already exists for this company.`,
        );
      if (employeeExist.bvn === bvn)
        errors.push(`BVN "${bvn}" already exists for this company.`);
      if (employeeExist.nin === nin)
        errors.push(`NIN "${nin}" already exists for this company.`);
    }

    if (email && emailExists) {
      errors.push(`Email "${email}" already exists for this company.`);
    }

    // Check for financial identifier duplicates
    if (financialIdExists) {
      if (taxId && financialIdExists.taxId === taxId) {
        errors.push(`Tax ID "${taxId}" already exists for this company.`);
      }
      if (pensionId && financialIdExists.pensionId === pensionId) {
        errors.push(
          `Pension ID "${pensionId}" already exists for this company.`,
        );
      }
      if (pfa && financialIdExists.pfa === pfa) {
        errors.push(`PFA "${pfa}" already exists for this company.`);
      }
    }

    // 2. Validate required relations
    const [branch, jobTitle, salaryPackage] = await Promise.all([
      this.databaseService.branch.findUnique({
        where: { name: `${companyId}|${branchName}`, companyId },
      }),
      this.databaseService.jobTitle.findUnique({
        where: { name: `${companyId}|${jobTitleName}`, companyId },
      }),
      this.databaseService.salaryPackage.findUnique({
        where: { name: `${companyId}|${salaryPackageName}`, companyId },
      }),
    ]);

    if (!branch) errors.push(`Branch "${branchName}" not found.`);
    if (!jobTitle) errors.push(`Job Title "${jobTitleName}" not found.`);
    if (!salaryPackage)
      errors.push(`Salary Package "${salaryPackageName}" not found.`);

    // 3. Optional checks
    const optionalChecks = [
      {
        label: 'Unit',
        name: unitName,
        fn: () =>
          this.databaseService.unit.findUnique({
            where: { name: `${companyId}|${unitName}` },
          }),
      },
      {
        label: 'Grade Level',
        name: gradeLevelName,
        fn: () =>
          this.databaseService.gradeLevel.findUnique({
            where: { name: `${companyId}|${gradeLevelName}` },
          }),
      },
      {
        label: 'Job Grade',
        name: jobGradeName,
        fn: () =>
          this.databaseService.jobGrade.findUnique({
            where: { name: `${companyId}|${jobGradeName}`, companyId },
          }),
      },
      {
        label: 'Contract Type',
        name: contractTypeName,
        fn: () =>
          this.databaseService.contractType.findUnique({
            where: { name: `${companyId}|${contractTypeName}` },
          }),
      },
      {
        label: 'Sub Branch',
        name: subBranchName,
        fn: () =>
          this.databaseService.subBranch.findUnique({
            where: { name: `${companyId}|${subBranchName}` },
          }),
      },
      {
        label: 'Department',
        name: departmentName,
        fn: () =>
          this.databaseService.department.findUnique({
            where: { name: `${companyId}|${departmentName}` },
          }),
      },
    ];

    await Promise.all(
      optionalChecks.map(async ({ label, name, fn }) => {
        if (name) {
          const exists = await fn();
          if (!exists) {
            errors.push(`${label} "${name}" not found.`);
          }
        }
      }),
    );

    // Final check
    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return true;
  }

  async createBankAccount(payload: CreateBankAccountDto) {
    console.log(payload);

    try {
      const { userToken, userBankCode } = await this.powerbankAuthenticate();

      const response$ = this.httpService.post(
        `${this.powerBankCreateAccountUrlDev}`,
        {
          ...payload,
          requestType: 'customerCreate',
          bankCode: userBankCode,
          createCustomer: 'YES',
          customerMaker: 'SYSTEM',
          customerType: 'CUSTOMER',
        },
        {
          headers: {
            Authorization: `${userToken}`,
            'Content-Type': 'application/json',
          },
        },
      ) as Observable<
        AxiosResponse<{
          customerId: string;
          accountNumber: string;
          responseCode: string;
          responseMessage: string;
        }>
      >;

      const response = (await lastValueFrom(response$)).data;

      return response;
    } catch (error: unknown) {
      console.log(error);

      throw new Error('Account creation failed');
    }
  }

  async createCustomBankAccount(payload: CreateBankAccountDto) {
    console.log(payload);

    try {
      const { userToken, userBankCode } = await this.powerbankAuthenticate();

      const response$ = this.httpService.post(
        `${this.powerBankUploadAccountUrlDev}`,
        {
          data: [{ ...payload }],
          bankCode: userBankCode,
          createCustomer: 'YES',
          maker: 'SYSTEM',
          // customerType: 'CUSTOMER',
          requestType: 'bulkUpload',
        },
        {
          headers: {
            Authorization: `${userToken}`,
            'Content-Type': 'application/json',
          },
        },
      ) as Observable<
        AxiosResponse<{
          customerId: string;
          accountNumber: string;
          responseCode: string;
          responseMessage: string;
        }>
      >;

      const response = (await lastValueFrom(response$)).data;

      console.log(response);

      return response;
    } catch (error: unknown) {
      console.log(error);

      throw new Error('Account creation failed');
    }
  }

  async getEmployeeBatch(token: string) {
    try {
      const decodedToken = await this.authTokenService.decodeToken(token);

      const employeeJobs = await this.databaseService.bulkEmployeeJob.findMany({
        where: {
          companyId: decodedToken.companyId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return employeeJobs;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async getEmployeeBatchRecord(jobId: string) {
    try {
      const employeeJobRecord =
        await this.databaseService.bulkEmployeeRecord.findMany({
          where: {
            jobId,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

      return employeeJobRecord;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async findCompanyEmployees(token: string) {
    try {
      const decodedToken = await this.authTokenService.decodeToken(token);

      const employees = await this.databaseService.employee.findMany({
        where: {
          companyId: decodedToken.companyId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return employees.map((emp) => ({
        ...emp,
        staffCode: emp.staffCode ? emp.staffCode.split('|')[1] : emp.staffCode,
        unitName: emp.unitName ? emp.unitName.split('|')[1] : emp.unitName,
        contractTypeName: emp.contractTypeName
          ? emp.contractTypeName.split('|')[1]
          : emp.contractTypeName,
        departmentName: emp.departmentName
          ? emp.departmentName.split('|')[1]
          : emp.departmentName,
        jobTitleName: emp.jobTitleName
          ? emp.jobTitleName.split('|')[1]
          : emp.jobTitleName,
        gradeLevelName: emp.gradeLevelName
          ? emp.gradeLevelName.split('|')[1]
          : emp.gradeLevelName,
        jobClusterName: emp.jobClusterName
          ? emp.jobClusterName.split('|')[1]
          : emp.jobClusterName,
        salaryPackageName: emp.salaryPackageName
          ? emp.salaryPackageName.split('|')[1]
          : emp.salaryPackageName,
        jobGradeName: emp.jobGradeName
          ? emp.jobGradeName.split('|')[1]
          : emp.jobGradeName,
        branchName: emp.branchName
          ? emp.branchName.split('|')[1]
          : emp.branchName,
        bvn: emp.bvn
          ? emp.bvn.length < 11
            ? '***********'
            : `${emp.bvn.slice(0, 3)}******${emp.bvn.slice(9, 11)}`
          : '***********',
        nin: emp.nin
          ? emp.nin.length < 11
            ? '***********'
            : `${emp.nin.slice(0, 3)}******${emp.nin.slice(9, 11)}`
          : '***********',
      }));
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async findEmployeeByEmail(payload: {
    email: FindEmployeeByEmailDto['email'];
    token?: string;
  }) {
    try {
      const decodedToken = await this.authTokenService.decodeToken(
        payload.token,
      );
      const employee = await this.databaseService.employee.findFirst({
        where: {
          companyId: decodedToken.companyId,
          email: payload.email,
        },
      });

      return employee;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async createEmployee(payload: { data: CreateEmployeeDto; token?: string }) {
    try {
      const decodedToken = await this.authTokenService.decodeToken(
        payload.token,
      );

      return await this.authorizationRequestMaker.queueRequest({
        payload: payload.data,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.EMPLOYEE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async createBulkEmployee(payload: {
    data: BulkCreateEmployeeDto;
    token?: string;
  }) {
    try {
      const decodedToken = await this.authTokenService.decodeToken(
        payload.token,
      );

      return await this.authorizationRequestMaker.queueRequest({
        payload: payload.data.employees,
        action: ACTIONS_CONSTANT.bulk_create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.EMPLOYEE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async createEmployeeAndBankAccount(payload: {
    data: CreateEmployeeDto;
    companyId: string;
    approvedBy: string;
    requestedBy: string;
  }) {
    const { data, companyId, approvedBy, requestedBy } = payload;

    // 1. Validate employee data
    await this.validateCreateEmployee({ ...data, companyId });

    const companyData = await this.databaseService.company.findUnique({
      where: {
        id: companyId,
      },
    });

    // 2. KYC check (external API)
    const kycResponse = await this.kycService.validateBvn({
      bvn: data.bvn,
      firstname: data.firstName,
      lastname: data.lastName,
    });

    if (kycResponse.status.status !== 'verified') {
      throw new BadRequestException('KYC validation failed');
    }

    // 3. Generate staff code
    const lastEmployee = await this.databaseService.employee.findFirst({
      where: { companyId },
      orderBy: { createdAt: 'desc' },
      select: { staffCode: true },
    });

    let staffCode: string;

    if (data.staffCode) {
      staffCode = `${companyId}|${data.staffCode}`;
    } else {
      let nextCode = '0001';

      if (lastEmployee?.staffCode) {
        const parts = lastEmployee.staffCode.split('-');
        const lastPart = parts.pop();

        if (lastPart && !isNaN(Number(lastPart))) {
          const incremented = (Number(lastPart) + 1)
            .toString()
            .padStart(4, '0');
          nextCode = incremented;
        }
      }

      staffCode = `${companyId}|${nextCode}`;
    }
    console.log(staffCode);

    // 4. Create employee first
    const createdEmployee = await this.databaseService.$transaction(
      async (tx) => {
        const employee = await tx.employee.create({
          data: {
            ...data,
            staffCode,
            companyId,
            kycVerified: true,
            accountStatus: 'PENDING', // temporary until account is created
            approvedBy,
            createdBy: requestedBy,
            jobGradeName: data.jobGradeName
              ? `${companyId}|${data.jobGradeName}`
              : undefined,
            jobClusterName: undefined,
            jobTitleName: `${companyId}|${data.jobTitleName}`,
            unitName: data.unitName
              ? `${companyId}|${data.unitName}`
              : undefined,
            departmentName: data.departmentName
              ? `${companyId}|${data.departmentName}`
              : undefined,
            branchName: `${companyId}|${data.branchName}`,
            subBranchName: data.subBranchName
              ? `${companyId}|${data.subBranchName}`
              : undefined,
            gradeLevelName: data.gradeLevelName
              ? `${companyId}|${data.gradeLevelName}`
              : undefined,
            salaryPackageName: `${companyId}|${data.salaryPackageName}`,
            contractTypeName: data.contractTypeName
              ? `${companyId}|${data.contractTypeName}`
              : undefined,
          },
        });

        return employee;
      },
    );

    const employeeCount = await this.databaseService.company.count({
      where: {
        id: companyId,
      },
    });

    const accountNumber = companyData?.accountPrefix
      ? String(Number(companyData.accountPrefix) + employeeCount + 1)
      : undefined;

    // 5. Create bank account (external call)
    const bankResponse = await this.createCustomBankAccount({
      customerFirstName: kycResponse.bvn.firstname,
      customerMiddleName: data.middleName ?? '',
      customerLastName: kycResponse.bvn.lastname,
      customerBvn: data.bvn,
      customerPhoneNumber: this.formatPhoneNumber(data.phone1),
      customerCountryCode: '234',
      customerAddress: data.residentialAddress,
      customerCountry: data.residentialCountry,
      customerDob: this.formatDate({ dateStr: data.birthday }),
      customerEmail: data.email,
      accountCurrency: 'NGN',
      accountNumber,
      customerGender: data.gender,
      customerNin: data.nin,
      customerAccountOfficer: '**********',
      customerAge:
        new Date().getFullYear() - Number(data.birthday.split('/')[2]),
      customerCity: data.residentialLocalGovt,
      customerState: data.residentialState,
      accountType: 'SAVINGS',
      accountClass: companyData?.accountClass || '',
      customerBranchCode: '0001',
      customerBankCode: '00001',
      customerIncorporationDate: new Date().toDateString(),
      customerNextOfKinName: data.nextOfKinFullName,
      customerNextOfKinPhoneNumber: data.nextOfKinPhoneNumber,
      source: 'MOBILE',
      userCountryCode: '234',
    });

    if (bankResponse.responseCode !== '00') {
      // Optional: delete the employee record to rollback manually
      await this.databaseService.employee.delete({
        where: { id: createdEmployee.id },
      });

      throw new BadRequestException(
        bankResponse.responseMessage ?? 'Bank account creation failed',
      );
    }

    // 6. Update employee with account info
    await this.databaseService.employee.update({
      where: { id: createdEmployee.id },
      data: {
        accountNumber,
        accountStatus: 'ACTIVE',
      },
    });

    return true;
  }

  async acceptEmployeeAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const newEmpData = JSON.parse(queue.data) as CreateEmployeeDto;

        const newEmp = await this.createEmployeeAndBankAccount({
          approvedBy,
          companyId,
          data: newEmpData,
          requestedBy,
        });

        return newEmp;
      }

      case 'CREATE_BULK': {
        const newEmpDatas = JSON.parse(queue.data) as CreateEmployeeDto[];

        console.log(newEmpDatas);

        const newJob = await this.databaseService.bulkEmployeeJob.create({
          data: {
            createdBy: requestedBy,
            approvedBy,
            totalCount: newEmpDatas.length,
            companyId,
          },
        });

        const jobRecords = newEmpDatas.map((newEmpData) => ({
          jobId: newJob.id,
          payload: JSON.parse(JSON.stringify(newEmpData)),
        }));

        await this.databaseService.bulkEmployeeRecord.createMany({
          data: jobRecords,
        });

        return true;
      }

      default:
        return false;
    }
  }

  checkStaffAccountStatus() {
    throw new NotImplementedException('API has not been implemented');
  }
}
