import { <PERSON>, Get, Param, <PERSON>, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { SwaggerService } from 'src/swagger/swagger.service';
import { AuthorizationService } from './authorization-queue.service';
import { ACTIONS_CONSTANT } from './constants/entity.constants';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { MODULE_CONSTANT } from './constants/module.constant';

@ApiTags('Authorization')
@Controller('authorization')
export class AuthorizationController {
  constructor(private readonly authorizationService: AuthorizationService) {}

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all authorization queue',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.QUEUE}|${ACTIONS_CONSTANT.view}`)
  @Get()
  getAuthorizationQueue(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.authorizationService.getAuthorizationQueue(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Accept authorization request',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.QUEUE}|${ACTIONS_CONSTANT.update}`)
  @Patch('/accept/:id')
  async acceptAuthorizationQueue(
    @Param('id') id: string,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    await this.authorizationService.acceptRequest({
      token: token!,
      queueId: id,
    });
    return null;
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Reject authorization request',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.QUEUE}|${ACTIONS_CONSTANT.update}`)
  @Patch('/reject/:id')
  async rejectAuthorizationQueue(
    @Param('id') id: string,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    await this.authorizationService.rejectRequest({
      token: token!,
      queueId: id,
    });

    return null;
  }
}
