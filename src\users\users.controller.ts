import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateUserDto } from './dto/create-user.dto';
import { DeleteUserDto } from './dto/delete-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UsersService } from './users.service';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersServices: UsersService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new account user with the provided details.',
    dto: CreateUserDto,
    statusCodes: [
      {
        status: 201,
        description: 'User successfully created',
        type: CreateUserDto, // Use DTO class here
        schema: {
          example: {
            email: '<EMAIL>',
            password: 'strongpassword123',
            accountId: 1,
            twoFactorEnabled: false,
          },
        },
      },
      {
        status: 400,
        description: 'Bad Request: Invalid input data',
        schema: {
          example: {
            message: 'Validation failed',
            error: 'Bad Request',
          },
        },
      },
      { status: 409, description: 'Conflict: User already exists' },
    ],
  })
  @RequirePrivilege('USER|CREATE')
  @Post()
  createUser(@Req() request: Request, @Body() createUserDto: CreateUserDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.usersServices.createUserRequest({
      token: token!,
      payload: createUserDto,
    });
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update a user with the provided details.',
    dto: CreateUserDto,
    statusCodes: [
      {
        status: 201,
        description: 'User successfully created',
        type: CreateUserDto, // Use DTO class here
        schema: {
          example: {
            email: '<EMAIL>',
            password: 'strongpassword123',
            accountId: 1,
            twoFactorEnabled: false,
          },
        },
      },
      {
        status: 400,
        description: 'Bad Request: Invalid input data',
        schema: {
          example: {
            message: 'Validation failed',
            error: 'Bad Request',
          },
        },
      },
      { status: 409, description: 'Conflict: User already exists' },
    ],
  })
  @RequirePrivilege('USER|UPDATE')
  @Post('/update')
  updateUser(@Req() request: Request, @Body() payload: UpdateUserDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.usersServices.updateUserRequest({
      token: token!,
      payload,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete a user with the provided details.',
    dto: DeleteUserDto,
    statusCodes: [
      {
        status: 201,
        description: 'User successfully created',
        type: CreateUserDto, // Use DTO class here
        schema: {
          example: {
            email: '<EMAIL>',
            password: 'strongpassword123',
            accountId: 1,
            twoFactorEnabled: false,
          },
        },
      },
      {
        status: 400,
        description: 'Bad Request: Invalid input data',
        schema: {
          example: {
            message: 'Validation failed',
            error: 'Bad Request',
          },
        },
      },
      { status: 409, description: 'Conflict: User already exists' },
    ],
  })
  @RequirePrivilege('USER|DELETE')
  @Post('/delete')
  deleteUser(@Req() request: Request, @Body() payload: DeleteUserDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.usersServices.deleteUserRequest({
      token: token!,
      payload,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all users.',
    statusCodes: [],
  })
  @RequirePrivilege('USER|VIEW')
  @Get()
  getUsers(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.usersServices.getUsers(token!);
  }
}
