import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateContractTypeDto } from './dto/create-contact-type.dto';
import { DeleteContractTypeDto } from './dto/delete-contract-type.dto';
import { UpdateContractTypeDto } from './dto/update-contract-type.dto';

@Injectable()
export class ContractTypeService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findContractTypeById(id: string, companyId: string) {
    const contractType = await this.databaseService.contractType.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return contractType;
  }

  async findContractType({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // If identifier is a number, search by id, otherwise by name
    const contractType = await this.databaseService.contractType.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return contractType;
  }

  async findContractTypeByName(name: string, companyId: string) {
    const contractType = await this.databaseService.contractType.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return contractType;
  }

  async findContractTypeByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const contractType = await this.databaseService.contractType.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return contractType;
  }

  async getContractTypes(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const contractTypes = await this.databaseService.contractType.findMany({
        where: {
          companyId,
          status: {
            not: 'INACTIVE',
          },
        },
        include: {
          _count: {
            select: {
              employees: true,
            },
          },
        },
      });

      return contractTypes.map((contractType) => ({
        ...contractType,
        name: contractType.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptContractTypeAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateContractTypeDto;

        await this.databaseService.contractType.create({
          data: {
            name: `${companyId}|${name}`,
            description,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateContractTypeDto;

        await this.updateContractType({
          companyId,
          payload,
        });

        return true;
      }

      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteContractTypeDto;

        await this.deleteContractType(payload);

        return true;
      }
      default:
        return false;
    }
  }

  async createContractType({
    payload,
    token,
  }: {
    payload: CreateContractTypeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the contract type already exists by name
    const contractTypeExist = await this.findContractTypeByName(
      payload.name,
      decodedToken.companyId,
    );

    if (contractTypeExist) {
      throw new ConflictException('Contract type already exists');
    }
    await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.CONTRACT_TYPE,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateContractTypeRequest({
    payload,
    token,
  }: {
    payload: UpdateContractTypeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the contract type exists
    const existingContractType =
      await this.databaseService.contractType.findUnique({
        where: {
          id: payload.id,
          companyId: decodedToken.companyId,
        },
      });

    if (!existingContractType) {
      throw new NotFoundException('Contract type not found.');
    }

    // Check if another contract type exists with the same name (excluding current one)
    if (payload.name) {
      const existingContractTypeName =
        await this.findContractTypeByNameExcludingId(
          payload.name,
          decodedToken.companyId,
          payload.id,
        );

      if (existingContractTypeName) {
        throw new ConflictException('Contract type already exists');
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.CONTRACT_TYPE,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateContractType({
    payload,
    companyId,
  }: {
    payload: UpdateContractTypeDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    const updateData: Prisma.ContractTypeUpdateInput = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description) {
      updateData.description = description;
    }

    await this.databaseService.contractType.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteContractTypeRequest({
    payload,
    token,
  }: {
    payload: DeleteContractTypeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const contractTypeExist = await this.findContractTypeById(
      payload.id,
      decodedToken.companyId,
    );

    if (!contractTypeExist) {
      throw new NotFoundException('Contract type not found');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload: { ...payload, name: contractTypeExist.name.split('|')[1] },
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.CONTRACT_TYPE,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async deleteContractType(payload: DeleteContractTypeDto) {
    const { id } = payload;

    await this.databaseService.contractType.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }
}
