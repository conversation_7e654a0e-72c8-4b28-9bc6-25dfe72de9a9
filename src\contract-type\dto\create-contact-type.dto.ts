import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

export class CreateContractTypeDto {
  @ApiProperty({
    description: 'Title of the contract type. Must be unique within a company.',
    maxLength: 255,
    example: 'Full-time',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    example: 'All management staff',
    description: 'A short description of the contract type',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}
