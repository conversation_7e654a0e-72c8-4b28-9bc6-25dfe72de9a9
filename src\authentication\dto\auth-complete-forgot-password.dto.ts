import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsInt, IsOptional, IsString } from 'class-validator';

export class AuthCompleteForgotPasswordDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  accountId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiProperty()
  @IsEmail({}, { message: 'Kindly provide a valid email' })
  email: string;

  @ApiProperty()
  password: string;

  @ApiProperty()
  otp: string;
}
